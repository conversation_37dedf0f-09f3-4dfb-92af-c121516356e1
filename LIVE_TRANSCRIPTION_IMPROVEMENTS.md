# Live Transcription Hook Improvements

## Overview
This document outlines the improvements made to the `use-live-transcription.ts` hook to address speaker diarization issues, reduce UI jitter, and follow Speechmatics best practices.

## Issues Addressed

### 1. Speaker Diarization Not Working Well
**Problem**: The speaker diarization couldn't detect all speakers effectively.

**Solution**: 
- Reduced `max_speakers` from 50 to 10 for better accuracy
- Increased `speaker_sensitivity` to 0.7 for better speaker detection
- Added `prefer_current_speaker: true` to reduce incorrect speaker switching
- Optimized `max_delay` to 2.0 seconds for better accuracy vs latency balance

### 2. Text Moving Too Fast During Transcription
**Problem**: Rapid partial transcript updates caused UI jitter and poor user experience.

**Solution**:
- Implemented throttling for partial transcripts with 300ms delay
- Added `updatePartialSegmentThrottled` function to batch partial updates
- Separated final and partial transcript handling for better performance

### 3. Speaker Display Showing "Speaker 1" Instead of "S1"
**Problem**: Speaker IDs were displayed as "Speaker 1", "Speaker 2" instead of the more compact "S1", "S2" format.

**Solution**:
- Added `formatSpeakerId` helper function to convert speaker IDs
- Automatically converts "Speaker 1" → "S1", "Speaker 2" → "S2", etc.
- Applied formatting consistently across all transcript segments

### 4. Configuration Not Following Best Practices
**Problem**: The Speechmatics configuration wasn't optimized according to their documentation.

**Solution**:
- Updated configuration to follow Speechmatics Real-Time API best practices
- Added proper speaker diarization configuration with sensitivity settings
- Implemented better error handling and resource cleanup

## Technical Improvements

### New Constants
```typescript
const PARTIAL_TRANSCRIPT_THROTTLE = 300; // ms - reduce UI jitter
const MAX_SPEAKERS_DEFAULT = 10; // Reduced for better accuracy
const SPEAKER_SENSITIVITY = 0.7; // Higher sensitivity for better detection
const MAX_DELAY = 2.0; // Balanced delay for better accuracy vs latency
```

### Enhanced Configuration
```typescript
speaker_diarization_config: {
  max_speakers: MAX_SPEAKERS_DEFAULT,
  speaker_sensitivity: SPEAKER_SENSITIVITY,
  prefer_current_speaker: true,
}
```

### Throttling Implementation
- Added `partialTranscriptTimeoutRef` and `pendingPartialSegmentRef` for throttling
- Implemented `updatePartialSegmentThrottled` function
- Separated immediate updates for final transcripts from throttled partial updates

### Improved Cleanup
- Added cleanup for partial transcript timeouts
- Enhanced speaker audio resource cleanup
- Better memory management for audio buffers

## Benefits

1. **Better Speaker Detection**: Reduced false positives and improved speaker identification accuracy
2. **Smoother UI Experience**: Throttled partial updates reduce visual jitter
3. **Consistent Speaker Labeling**: Compact "S1", "S2" format for better readability
4. **Optimized Performance**: Better resource management and cleanup
5. **Following Best Practices**: Configuration aligned with Speechmatics recommendations

## Testing Recommendations

1. Test with multiple speakers (2-4 people) to verify improved diarization
2. Verify that speaker IDs display as "S1", "S2" instead of "Speaker 1", "Speaker 2"
3. Check that partial transcripts update smoothly without excessive movement
4. Test dual-stream mode (microphone + screen audio) functionality
5. Verify proper cleanup when stopping transcription

## Future Enhancements

1. **Dynamic Speaker Sensitivity**: Allow users to adjust speaker sensitivity based on audio quality
2. **Speaker Name Mapping**: Allow users to assign custom names to detected speakers
3. **Confidence Thresholds**: Add configurable confidence thresholds for speaker detection
4. **Audio Quality Detection**: Automatically adjust settings based on detected audio quality
