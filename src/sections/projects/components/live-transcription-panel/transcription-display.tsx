import type { SxProps } from '@mui/material';
import type { LiveTranscriptionSegment } from 'src/types';

import { useMemo } from 'react';

import { Box, Fade, Paper, Stack, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

interface Props {
  segments: LiveTranscriptionSegment[];
  sx?: SxProps;
}

const TranscriptionDisplay: React.FC<Props> = ({ segments, sx = {} }) => {
  const groupSegmentsByPunctuation = (segmentList: LiveTranscriptionSegment[]) => {
    const groups: LiveTranscriptionSegment[][] = [];
    let currentGroup: LiveTranscriptionSegment[] = [];

    segmentList.forEach((segment, index) => {
      const previousSegment = segmentList[index - 1];

      // Start a new group if speaker changes
      if (
        currentGroup.length > 0 &&
        previousSegment &&
        segment.speakerId !== previousSegment.speakerId
      ) {
        groups.push([...currentGroup]);
        currentGroup = [];
      }

      currentGroup.push(segment);

      // Check if this segment ends a sentence
      const text = segment.text.trim();
      if (text.endsWith('.') || text.endsWith('?') || text.endsWith('!')) {
        groups.push([...currentGroup]);
        currentGroup = [];
      }
    });

    // Add remaining segments as partial group if any
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    return groups;
  };

  const renderTranscribeItem = (segmentGroup: LiveTranscriptionSegment[], isPartial: boolean) => {
    if (!segmentGroup.length) return null;

    // Combine all segment texts in the group with proper spacing
    const combinedText = segmentGroup
      .map((s) => s.text.trim())
      .filter((text) => text.length > 0)
      .join(' ')
      .trim();
    const latestTimestamp = segmentGroup[segmentGroup.length - 1]?.timestamp;
    const speakerId = segmentGroup[0]?.speakerId;

    // Generate a unique key for the group
    const groupKey =
      segmentGroup[0]?.id ||
      `group-${segmentGroup
        .map((s) => s.text)
        .join('-')
        .slice(0, 20)}`;

    return (
      <Fade in key={groupKey} timeout={300}>
        <Box sx={{ mb: 1.5, display: 'flex', justifyContent: 'flex-start' }}>
          <Paper
            elevation={isPartial ? 0 : 1}
            sx={{
              p: 1.5,
              maxWidth: '95%',
              bgcolor: isPartial ? 'grey.100' : 'primary.light',
              borderRadius: 2,
              position: 'relative',
              border: isPartial ? '1px dashed' : 'none',
              borderColor: isPartial ? 'grey.300' : 'transparent',
              transition: 'all 0.3s ease',
            }}
          >
            {/* Speaker Name Header */}
            {speakerId && (
              <Box sx={{ mb: 1 }}>
                <Box
                  sx={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: 0.5,
                    px: 1.5,
                    py: 0.5,
                    bgcolor: isPartial ? 'grey.300' : 'primary.dark',
                    borderRadius: 1.5,
                    mb: 0.5,
                  }}
                >
                  <Iconify
                    icon="material-symbols:person"
                    width={12}
                    color={isPartial ? 'text.primary' : 'primary.contrastText'}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: 11,
                      fontWeight: 600,
                      color: isPartial ? 'text.primary' : 'primary.contrastText',
                      textTransform: 'capitalize',
                      letterSpacing: 0.3,
                    }}
                  >
                    {speakerId}
                  </Typography>
                </Box>
              </Box>
            )}

            <Typography
              variant="body2"
              color={isPartial ? 'text.secondary' : 'primary.contrastText'}
              sx={{
                wordBreak: 'break-word',
                lineHeight: 1.4,
                fontSize: 13,
              }}
            >
              {combinedText}
              {isPartial && (
                <Box
                  component="span"
                  sx={{
                    ml: 1,
                    opacity: 0.6,
                    fontWeight: 'bold',
                    animation: 'blink 1.5s infinite',
                    '@keyframes blink': {
                      '0%, 50%': { opacity: 0.6 },
                      '51%, 100%': { opacity: 0.2 },
                    },
                  }}
                >
                  ●●●
                </Box>
              )}
            </Typography>

            <Typography
              variant="caption"
              sx={{
                fontSize: 10,
                opacity: 0.7,
                color: isPartial ? 'text.disabled' : 'primary.contrastText',
                mt: 0.5,
              }}
            >
              {latestTimestamp ? new Date(latestTimestamp).toLocaleTimeString() : 'Now'}
            </Typography>
          </Paper>
        </Box>
      </Fade>
    );
  };

  const renderEmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 3,
        opacity: 0.6,
      }}
    >
      <Iconify
        icon="material-symbols:chat-bubble-outline"
        width={36}
        color="text.disabled"
        sx={{ mb: 1.5 }}
      />
      <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ fontSize: 13 }}>
        No transcription yet
      </Typography>
      <Typography variant="caption" color="text.disabled" textAlign="center" sx={{ fontSize: 11 }}>
        Start recording to see live transcription
      </Typography>
    </Box>
  );

  const groupedSegments = useMemo(() => {
    if (!segments.length) return [];
    return groupSegmentsByPunctuation(segments);
  }, [segments]);

  return (
    <Paper
      elevation={0}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        ...sx,
      }}
    >
      <Box sx={{ p: 1.5, borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="material-symbols:chat-bubble" width={16} />
            <Typography variant="subtitle2" sx={{ fontSize: 13 }}>
              Transcription
            </Typography>
          </Stack>
        </Stack>
      </Box>

      <Scrollbar
        sx={{
          flex: 1,
          minHeight: 300,
        }}
        slotProps={{
          contentSx: {
            p: 1.5,
          },
        }}
      >
        {groupedSegments.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            {renderEmptyState()}
          </Box>
        ) : (
          <Box>
            {groupedSegments.map((segmentGroup, index) => {
              // Check if this is the last group (partial sentence)
              const isPartial =
                index === groupedSegments.length - 1 &&
                !segmentGroup.some((segment) => {
                  const text = segment.text.trim();
                  return text.endsWith('.') || text.endsWith('?') || text.endsWith('!');
                });

              return renderTranscribeItem(segmentGroup, isPartial);
            })}
          </Box>
        )}
      </Scrollbar>
    </Paper>
  );
};

export default TranscriptionDisplay;
